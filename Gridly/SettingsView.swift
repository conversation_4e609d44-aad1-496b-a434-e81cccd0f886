import SwiftUI

struct SettingsView: View {
    @StateObject private var settingsManager = SettingsManager.shared
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(spacing: 20) {
            // محتوى الإعدادات
            VStack(alignment: .center, spacing: 15) {
                HStack {
                    Text("Launch at login")
                        .font(.body)
                        .foregroundColor(.primary)

                    Spacer()

                    Toggle("", isOn: $settingsManager.launchAtLogin)
                        .toggleStyle(CheckboxToggleStyle())
                }
                .padding(.horizontal, 20)
            }
        }
        .frame(width: 180, height: 80)
        .background(Color(NSColor.windowBackgroundColor))
        .cornerRadius(15)
        .onAppear {
            // التحقق من حالة التشغيل التلقائي عند فتح النافذة
            settingsManager.launchAtLogin = settingsManager.checkLaunchAtLoginStatus()
        }
    }
    
    private func closeSettings() {
        // إغلاق نافذة الإعدادات
        if let window = NSApplication.shared.windows.first(where: { $0.title == "Settings" }) {
            window.close()
        }
    }
}

// نافذة الإعدادات المخصصة
class SettingsWindowController: NSWindowController {
    convenience init() {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 180, height: 80),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        window.title = "Settings"
        window.center()
        window.isReleasedWhenClosed = false
        window.level = .floating
        
        // تعيين محتوى النافذة
        let hostingController = NSHostingController(rootView: SettingsView())
        window.contentViewController = hostingController
        
        self.init(window: window)
    }
    
    func showSettings() {
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
}

#Preview {
    SettingsView()
}
