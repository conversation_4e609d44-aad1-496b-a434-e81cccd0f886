import SwiftUI
import UserNotifications
import ServiceManagement

@main
struct GridlyApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        Settings {
            EmptyView()
        }
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusItem: NSStatusItem?
    var popover: NSPopover?
    var customWindow: NSWindow?
    var settingsWindowController: SettingsWindowController?

    func applicationDidFinishLaunching(_ notification: Notification) {
        // إظهار أيقونة التطبيق في الـ Dock
        NSApp.setActivationPolicy(.regular)

        // طلب صلاحيات الإشعارات
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound]) { granted, error in
            if let error = error {
                print("خطأ في طلب صلاحيات الإشعارات: \(error)")
            }
        }

        // إنشاء status item في menu bar
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.squareLength)

        if let button = statusItem?.button {
            button.image = NSImage(named: "Image")
            button.action = #selector(togglePopover)
            button.target = self

            // إضافة قائمة سياقية للضغط بالزر الأيمن
            button.sendAction(on: [.leftMouseUp, .rightMouseUp])
        }

        // إنشاء popover مع إزالة الحدود
        popover = NSPopover()
        popover?.contentSize = NSSize(width: 360, height: 270)
        popover?.behavior = .transient
        popover?.appearance = nil

        // محاولة إزالة الحدود من الـ popover
        let hostingController = NSHostingController(rootView: WallGridView())
        popover?.contentViewController = hostingController

        // إزالة الحدود بعد الإنشاء
        DispatchQueue.main.async {
            if let popoverWindow = self.popover?.contentViewController?.view.window {
                popoverWindow.backgroundColor = NSColor.clear
                popoverWindow.isOpaque = false
                popoverWindow.hasShadow = false
            }
        }

        // إنشاء نافذة مخصصة بدون إطار كبديل
        createCustomWindow()

        // إنشاء نافذة الإعدادات
        settingsWindowController = SettingsWindowController()
    }

    func createCustomWindow() {
        let contentRect = NSRect(x: 0, y: 0, width: 360, height: 270)
        customWindow = NSWindow(
            contentRect: contentRect,
            styleMask: [],
            backing: .buffered,
            defer: false
        )

        customWindow?.isOpaque = false
        customWindow?.backgroundColor = NSColor.clear
        customWindow?.hasShadow = false
        customWindow?.level = .floating
        customWindow?.ignoresMouseEvents = false

        // إزالة أي حدود أو إطارات
        customWindow?.contentView?.wantsLayer = true
        customWindow?.contentView?.layer?.borderWidth = 0
        customWindow?.contentView?.layer?.cornerRadius = 0

        customWindow?.contentViewController = NSHostingController(rootView: WallGridView())

        // إضافة مراقب للضغط خارج النافذة للخروج من القائمة
        NSEvent.addGlobalMonitorForEvents(matching: [.leftMouseDown, .rightMouseDown]) { [weak self] event in
            if let window = self?.customWindow, window.isVisible {
                // التحقق من أن الضغطة خارج النافذة
                if !window.frame.contains(NSEvent.mouseLocation) {
                    window.orderOut(nil)
                }
            }
        }
    }

    @objc func togglePopover() {
        guard let button = statusItem?.button else { return }

        // التحقق من نوع الضغطة
        let event = NSApp.currentEvent
        if event?.type == .rightMouseUp {
            // إظهار قائمة سياقية للضغط بالزر الأيمن
            showContextMenu()
        } else {
            // إظهار/إخفاء النافذة المخصصة للضغط بالزر الأيسر
            guard let window = customWindow else { return }

            if window.isVisible {
                window.orderOut(nil)
            } else {
                // حساب موقع النافذة تحت الأيقونة
                let buttonFrame = button.window?.convertToScreen(button.convert(button.bounds, to: nil)) ?? NSRect.zero
                let windowOrigin = NSPoint(
                    x: buttonFrame.midX - window.frame.width / 2,
                    y: buttonFrame.minY - window.frame.height - 5
                )
                window.setFrameOrigin(windowOrigin)
                window.makeKeyAndOrderFront(nil)
            }
        }
    }

    func showContextMenu() {
        guard statusItem?.button != nil else { return }

        let menu = NSMenu()

        // إضافة عنصر الإعدادات
        let settingsItem = NSMenuItem(title: "Settings", action: #selector(showSettings), keyEquivalent: ",")
        settingsItem.target = self
        menu.addItem(settingsItem)

        // إضافة فاصل
        menu.addItem(NSMenuItem.separator())

        // إضافة عنصر Quit
        let quitItem = NSMenuItem(title: "Quit", action: #selector(quitApp), keyEquivalent: "q")
        quitItem.target = self
        menu.addItem(quitItem)

        // إظهار القائمة
        statusItem?.menu = menu
        statusItem?.button?.performClick(nil)
        statusItem?.menu = nil
    }

    @objc func showSettings() {
        settingsWindowController?.showSettings()
    }

    @objc func quitApp() {
        NSApplication.shared.terminate(nil)
    }
}
