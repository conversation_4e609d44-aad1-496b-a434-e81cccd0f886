import Foundation
import ServiceManagement

class SettingsManager: ObservableObject {
    static let shared = SettingsManager()
    
    @Published var launchAtLogin: Bool {
        didSet {
            UserDefaults.standard.set(launchAtLogin, forKey: "launchAtLogin")
            updateLaunchAtLogin()
        }
    }
    
    private init() {
        self.launchAtLogin = UserDefaults.standard.bool(forKey: "launchAtLogin")
    }
    
    private func updateLaunchAtLogin() {
        if #available(macOS 13.0, *) {
            // استخدام SMAppService للإصدارات الحديثة من macOS
            do {
                if launchAtLogin {
                    try SMAppService.mainApp.register()
                } else {
                    try SMAppService.mainApp.unregister()
                }
            } catch {
                print("خطأ في تحديث إعدادات التشغيل التلقائي: \(error)")
            }
        } else {
            // استخدام الطريقة القديمة للإصدارات الأقدم
            updateLaunchAtLoginLegacy()
        }
    }
    
    private func updateLaunchAtLoginLegacy() {
        let bundleIdentifier = Bundle.main.bundleIdentifier ?? ""
        
        if launchAtLogin {
            // إضافة التطبيق إلى قائمة التشغيل التلقائي
            if !SMLoginItemSetEnabled(bundleIdentifier as CFString, true) {
                print("فشل في تفعيل التشغيل التلقائي")
            }
        } else {
            // إزالة التطبيق من قائمة التشغيل التلقائي
            if !SMLoginItemSetEnabled(bundleIdentifier as CFString, false) {
                print("فشل في تعطيل التشغيل التلقائي")
            }
        }
    }
    
    // التحقق من حالة التشغيل التلقائي الحالية
    func checkLaunchAtLoginStatus() -> Bool {
        if #available(macOS 13.0, *) {
            return SMAppService.mainApp.status == .enabled
        } else {
            // للإصدارات الأقدم، نعتمد على UserDefaults
            return UserDefaults.standard.bool(forKey: "launchAtLogin")
        }
    }
    
    // إعادة تعيين جميع الإعدادات إلى القيم الافتراضية
    func resetToDefaults() {
        launchAtLogin = false
        UserDefaults.standard.removeObject(forKey: "launchAtLogin")
    }
}
