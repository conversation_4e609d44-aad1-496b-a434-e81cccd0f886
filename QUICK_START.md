# Gridly - Quick Start Guide

A macOS menu bar app for easy desktop wallpaper management.

## Features
- Menu bar application (no dock icon)
- 4 rectangular wallpaper slots in a 2×2 grid
- Clean icon-only interface (no image previews)
- Click empty slot to add wallpaper
- Click filled slot to apply wallpaper
- Auto-save wallpaper selections
- Success notifications

## How to Use

1. **Launch**: Run the app, look for grid icon in menu bar
2. **Open**: Click the menu bar icon to show popup
3. **Add**: Click empty slot (+ icon) to select image
4. **Apply**: Click slot with image to set as wallpaper
5. **Context Menu**: Right-click for additional options

## Build & Run

```bash
# Clone and build
cd Gridly
xcodebuild -project Gridly.xcodeproj -scheme Gridly build

# Or open in Xcode and press ⌘+R
```

## Requirements
- macOS 11.0+
- Xcode 13.0+ (for development)

## Key Files
- `wallgridApp.swift` - App entry point and menu bar setup
- `WallGridView.swift` - Main UI with grid layout
- `wallgrid.entitlements` - Required permissions

## Permissions
- File access (to select images)
- Notifications (success messages)
- Desktop wallpaper modification

That's it! Simple wallpaper management from your menu bar.
