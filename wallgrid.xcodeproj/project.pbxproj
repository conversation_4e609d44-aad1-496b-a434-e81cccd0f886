// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		456AD2E12E38F9B400EBBB0D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 456AD2CA2E38F9B300EBBB0D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 456AD2D12E38F9B300EBBB0D;
			remoteInfo = wallgrid;
		};
		456AD2EB2E38F9B400EBBB0D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 456AD2CA2E38F9B300EBBB0D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 456AD2D12E38F9B300EBBB0D;
			remoteInfo = wallgrid;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		456AD2D22E38F9B300EBBB0D /* wallgrid.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = wallgrid.app; sourceTree = BUILT_PRODUCTS_DIR; };
		456AD2E02E38F9B400EBBB0D /* wallgridTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = wallgridTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		456AD2EA2E38F9B400EBBB0D /* wallgridUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = wallgridUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B4D8E5090FF1471592D4F551 /* SettingsManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsManager.swift; sourceTree = "<group>"; };
		CE6C6E1909024264938383D2 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		456AD2D42E38F9B300EBBB0D /* wallgrid */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wallgrid;
			sourceTree = "<group>";
		};
		456AD2E32E38F9B400EBBB0D /* wallgridTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wallgridTests;
			sourceTree = "<group>";
		};
		456AD2ED2E38F9B400EBBB0D /* wallgridUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wallgridUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		456AD2CF2E38F9B300EBBB0D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		456AD2DD2E38F9B400EBBB0D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		456AD2E72E38F9B400EBBB0D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		456AD2C92E38F9B300EBBB0D = {
			isa = PBXGroup;
			children = (
				456AD2D42E38F9B300EBBB0D /* wallgrid */,
				456AD2E32E38F9B400EBBB0D /* wallgridTests */,
				456AD2ED2E38F9B400EBBB0D /* wallgridUITests */,
				456AD2D32E38F9B300EBBB0D /* Products */,
			);
			sourceTree = "<group>";
		};
		456AD2D32E38F9B300EBBB0D /* Products */ = {
			isa = PBXGroup;
			children = (
				456AD2D22E38F9B300EBBB0D /* wallgrid.app */,
				456AD2E02E38F9B400EBBB0D /* wallgridTests.xctest */,
				456AD2EA2E38F9B400EBBB0D /* wallgridUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		456AD2D12E38F9B300EBBB0D /* wallgrid */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 456AD2F42E38F9B400EBBB0D /* Build configuration list for PBXNativeTarget "wallgrid" */;
			buildPhases = (
				456AD2CE2E38F9B300EBBB0D /* Sources */,
				456AD2CF2E38F9B300EBBB0D /* Frameworks */,
				456AD2D02E38F9B300EBBB0D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				456AD2D42E38F9B300EBBB0D /* wallgrid */,
			);
			name = wallgrid;
			packageProductDependencies = (
			);
			productName = wallgrid;
			productReference = 456AD2D22E38F9B300EBBB0D /* wallgrid.app */;
			productType = "com.apple.product-type.application";
		};
		456AD2DF2E38F9B400EBBB0D /* wallgridTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 456AD2F72E38F9B400EBBB0D /* Build configuration list for PBXNativeTarget "wallgridTests" */;
			buildPhases = (
				456AD2DC2E38F9B400EBBB0D /* Sources */,
				456AD2DD2E38F9B400EBBB0D /* Frameworks */,
				456AD2DE2E38F9B400EBBB0D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				456AD2E22E38F9B400EBBB0D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				456AD2E32E38F9B400EBBB0D /* wallgridTests */,
			);
			name = wallgridTests;
			packageProductDependencies = (
			);
			productName = wallgridTests;
			productReference = 456AD2E02E38F9B400EBBB0D /* wallgridTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		456AD2E92E38F9B400EBBB0D /* wallgridUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 456AD2FA2E38F9B400EBBB0D /* Build configuration list for PBXNativeTarget "wallgridUITests" */;
			buildPhases = (
				456AD2E62E38F9B400EBBB0D /* Sources */,
				456AD2E72E38F9B400EBBB0D /* Frameworks */,
				456AD2E82E38F9B400EBBB0D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				456AD2EC2E38F9B400EBBB0D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				456AD2ED2E38F9B400EBBB0D /* wallgridUITests */,
			);
			name = wallgridUITests;
			packageProductDependencies = (
			);
			productName = wallgridUITests;
			productReference = 456AD2EA2E38F9B400EBBB0D /* wallgridUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		456AD2CA2E38F9B300EBBB0D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					456AD2D12E38F9B300EBBB0D = {
						CreatedOnToolsVersion = 16.4;
					};
					456AD2DF2E38F9B400EBBB0D = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 456AD2D12E38F9B300EBBB0D;
					};
					456AD2E92E38F9B400EBBB0D = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 456AD2D12E38F9B300EBBB0D;
					};
				};
			};
			buildConfigurationList = 456AD2CD2E38F9B300EBBB0D /* Build configuration list for PBXProject "wallgrid" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 456AD2C92E38F9B300EBBB0D;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 456AD2D32E38F9B300EBBB0D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				456AD2D12E38F9B300EBBB0D /* wallgrid */,
				456AD2DF2E38F9B400EBBB0D /* wallgridTests */,
				456AD2E92E38F9B400EBBB0D /* wallgridUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		456AD2D02E38F9B300EBBB0D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		456AD2DE2E38F9B400EBBB0D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		456AD2E82E38F9B400EBBB0D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		456AD2CE2E38F9B300EBBB0D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			
				D852594F8E444CCAA9809CA5 /* SettingsManager.swift in Sources */,
				EA684ED99F584E239D11618E /* SettingsView.swift in Sources */,);
			runOnlyForDeploymentPostprocessing = 0;
		};
		456AD2DC2E38F9B400EBBB0D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		456AD2E62E38F9B400EBBB0D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		456AD2E22E38F9B400EBBB0D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 456AD2D12E38F9B300EBBB0D /* wallgrid */;
			targetProxy = 456AD2E12E38F9B400EBBB0D /* PBXContainerItemProxy */;
		};
		456AD2EC2E38F9B400EBBB0D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 456AD2D12E38F9B300EBBB0D /* wallgrid */;
			targetProxy = 456AD2EB2E38F9B400EBBB0D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		456AD2F22E38F9B400EBBB0D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		456AD2F32E38F9B400EBBB0D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		456AD2F52E38F9B400EBBB0D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = wallgrid/wallgrid.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.Gridly;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		456AD2F62E38F9B400EBBB0D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = wallgrid/wallgrid.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.Gridly;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		456AD2F82E38F9B400EBBB0D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.GridlyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/wallgrid.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/wallgrid";
			};
			name = Debug;
		};
		456AD2F92E38F9B400EBBB0D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.GridlyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/wallgrid.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/wallgrid";
			};
			name = Release;
		};
		456AD2FB2E38F9B400EBBB0D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.wallgridUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = wallgrid;
			};
			name = Debug;
		};
		456AD2FC2E38F9B400EBBB0D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.wallgridUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = wallgrid;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		456AD2CD2E38F9B300EBBB0D /* Build configuration list for PBXProject "wallgrid" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				456AD2F22E38F9B400EBBB0D /* Debug */,
				456AD2F32E38F9B400EBBB0D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		456AD2F42E38F9B400EBBB0D /* Build configuration list for PBXNativeTarget "wallgrid" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				456AD2F52E38F9B400EBBB0D /* Debug */,
				456AD2F62E38F9B400EBBB0D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		456AD2F72E38F9B400EBBB0D /* Build configuration list for PBXNativeTarget "wallgridTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				456AD2F82E38F9B400EBBB0D /* Debug */,
				456AD2F92E38F9B400EBBB0D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		456AD2FA2E38F9B400EBBB0D /* Build configuration list for PBXNativeTarget "wallgridUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				456AD2FB2E38F9B400EBBB0D /* Debug */,
				456AD2FC2E38F9B400EBBB0D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 456AD2CA2E38F9B300EBBB0D /* Project object */;
}
