# Gridly - تطبيق تغيير خلفيات سطح المكتب

تطبيق macOS يعمل في شريط القوائم (Menu Bar) لتغيير خلفيات سطح المكتب بسهولة.

## المميزات

- **تطبيق Menu Bar**: يعمل في الخلفية ولا يشغل مساحة في الـ Dock
- **واجهة مبسطة**: 4 مربعات مستطيلة منظمة في شبكة 2×2
- **تصميم نظيف**: عرض الأيقونات فقط بدون صور لواجهة أكثر بساطة
- **إضافة سريعة**: اضغط على المربع الفارغ لإضافة خلفية جديدة
- **تطبيق فوري**: اضغط على المربع المحتوي على خلفية لتطبيقها
- **حفظ تلقائي**: يحفظ الخلفيات المختارة تلقائياً
- **إشعارات**: يظهر إشعار عند نجاح تطبيق الخلفية

## كيفية الاستخدام

### 1. تشغيل التطبيق
- قم بتشغيل التطبيق من Xcode أو من ملف .app
- ستظهر أيقونة شبكة في شريط القوائم العلوي

### 2. فتح النافذة المنبثقة
- اضغط على أيقونة التطبيق في شريط القوائم
- ستظهر نافذة تحتوي على 4 مربعات مستطيلة

### 3. إضافة خلفيات
- اضغط على أي مربع فارغ (يظهر علامة +)
- اختر صورة من جهازك
- ستظهر الصورة في المربع

### 4. تطبيق الخلفية
- اضغط على أي مربع يحتوي على صورة
- ستتغير خلفية سطح المكتب فوراً
- ستظهر رسالة تأكيد

### 5. القائمة السياقية
- اضغط بالزر الأيمن على أي مربع للحصول على خيارات إضافية:
  - تغيير الخلفية
  - تطبيق الخلفية

## المتطلبات

- macOS 11.0 أو أحدث
- Xcode 13.0 أو أحدث (للتطوير)

## التثبيت

### من الكود المصدري:
1. افتح المشروع في Xcode
2. اضغط على Build (⌘+B)
3. اضغط على Run (⌘+R)

### إنشاء ملف .app:
1. في Xcode، اختر Product > Archive
2. اختر Distribute App
3. اختر Copy App

## الصلاحيات المطلوبة

التطبيق يحتاج إلى الصلاحيات التالية:
- **قراءة الملفات**: لاختيار الصور من جهازك
- **الإشعارات**: لإظهار رسائل التأكيد
- **تغيير خلفية سطح المكتب**: لتطبيق الخلفيات الجديدة

## الملفات الرئيسية

- `wallgridApp.swift`: نقطة البداية والـ AppDelegate
- `WallGridView.swift`: واجهة المستخدم الرئيسية
- `ContentView.swift`: واجهة احتياطية (غير مستخدمة)

## التخصيص

يمكنك تخصيص التطبيق عبر تعديل:
- عدد المربعات في `WallGridView.swift`
- حجم النافذة المنبثقة
- أيقونة التطبيق في شريط القوائم

## استكشاف الأخطاء

### التطبيق لا يظهر في شريط القوائم
- تأكد من أن التطبيق يعمل
- تحقق من إعدادات النظام > الخصوصية والأمان

### لا يمكن تطبيق الخلفية
- تأكد من أن الصورة بتنسيق مدعوم (JPG, PNG, HEIC)
- تحقق من صلاحيات التطبيق

### الإشعارات لا تظهر
- اذهب إلى إعدادات النظام > الإشعارات
- تأكد من تفعيل إشعارات WallGrid

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
